<?php
/**
 * My Account page
 *
 * Bu template WooCommerce hesabim sayfasinda kullanilir
 * Kullanici kapak fotografi banner'i ve profil bilgileri eklenir
 */

defined( 'ABSPATH' ) || exit;

// Mevcut kullanici bilgilerini al
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$cover_photo_url = dmrthema_get_user_cover_photo( $user_id );
$avatar_url = get_avatar_url( $user_id, array( 'size' => 120 ) );

?>

<!-- Kullanici Kapak Fotografi Banner -->
<div class="user-account-banner" <?php if ( ! empty( $cover_photo_url ) ) : ?>style="background-image: url('<?php echo esc_url( $cover_photo_url ); ?>');"<?php endif; ?>>
    <div class="user-account-banner-overlay">
        <div class="user-account-profile">
            <div class="user-account-avatar">
                <img src="<?php echo esc_url( $avatar_url ); ?>" alt="<?php echo esc_attr( $current_user->display_name ); ?>" class="user-avatar-large">
            </div>
            <div class="user-account-info">
                <h1 class="user-account-name">Hosgeldin, <?php echo esc_html( $current_user->display_name ); ?>!</h1>
                <p class="user-account-email"><?php echo esc_html( $current_user->user_email ); ?></p>
                <p class="user-account-description">Hesap ozetinizi ve son aktivitelerinizi buradan takip edebilirsiniz.</p>
            </div>
        </div>
    </div>
</div>

<?php
/**
 * My Account navigation.
 *
 * @since 2.6.0
 */
do_action( 'woocommerce_account_navigation' ); ?>

<div class="woocommerce-MyAccount-content">
	<?php
		/**
		 * My Account content.
		 *
		 * @since 2.6.0
		 */
		do_action( 'woocommerce_account_content' );
	?>
</div>
